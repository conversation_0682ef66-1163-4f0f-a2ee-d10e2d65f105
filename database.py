from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, ForeignKey, <PERSON>olean, Text, Index, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import text
import datetime

# إنشاء محرك قاعدة البيانات مع تحسينات الأداء المتقدمة
engine = create_engine(
    'sqlite:///accounting.db',
    echo=False,
    connect_args={
        'check_same_thread': False,  # السماح بالوصول متعدد المؤشرات
        'timeout': 60,  # زيادة مهلة الاتصال إلى 60 ثانية
        'cached_statements': 1000,  # تخزين 1000 استعلام مؤقت
    },
    isolation_level='AUTOCOMMIT',  # مستوى العزل المناسب لـ SQLite
    pool_pre_ping=True,  # التحقق من صحة الاتصال قبل استخدامه
    pool_recycle=7200,  # إعادة تدوير الاتصالات كل ساعتين
    pool_size=50,  # زيادة حجم تجمع الاتصالات إلى 50
    max_overflow=100,  # السماح بـ 100 اتصال إضافي عند الحاجة
    pool_timeout=30,  # مهلة انتظار الحصول على اتصال
    pool_reset_on_return='commit',  # إعادة تعيين الاتصال عند الإرجاع
    execution_options={
        'autocommit': False
    }
)

# تحسين أداء SQLAlchemy المتقدم
from sqlalchemy import event
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """تعيين إعدادات SQLite المتقدمة لتحسين الأداء إلى 100%"""
    cursor = dbapi_connection.cursor()

    # إعدادات الأداء الأساسية
    cursor.execute("PRAGMA journal_mode=WAL")  # وضع WAL للأداء العالي
    cursor.execute("PRAGMA synchronous=NORMAL")  # توازن بين الأداء والأمان
    cursor.execute("PRAGMA cache_size=-64000")  # 64MB ذاكرة تخزين مؤقت
    cursor.execute("PRAGMA temp_store=MEMORY")  # جداول مؤقتة في الذاكرة
    cursor.execute("PRAGMA mmap_size=268435456")  # 256MB memory mapping
    cursor.execute("PRAGMA busy_timeout=60000")  # مهلة انتظار 60 ثانية

    # إعدادات الأداء المتقدمة
    cursor.execute("PRAGMA page_size=4096")  # حجم الصفحة الأمثل
    cursor.execute("PRAGMA wal_autocheckpoint=1000")  # نقطة تفتيش كل 1000 معاملة
    cursor.execute("PRAGMA optimize")  # تحسين تلقائي للفهارس
    cursor.execute("PRAGMA analysis_limit=1000")  # حد تحليل الإحصائيات
    cursor.execute("PRAGMA threads=4")  # استخدام 4 خيوط للمعالجة

    # إعدادات الذاكرة المتقدمة
    cursor.execute("PRAGMA cache_spill=FALSE")  # منع تسرب الذاكرة المؤقتة
    cursor.execute("PRAGMA query_only=FALSE")  # السماح بالكتابة
    cursor.execute("PRAGMA read_uncommitted=FALSE")  # منع القراءة غير المؤكدة
    cursor.execute("PRAGMA recursive_triggers=TRUE")  # تمكين المشغلات المتداخلة

    # إعدادات الأمان والموثوقية
    cursor.execute("PRAGMA foreign_keys=ON")  # تمكين المفاتيح الخارجية
    cursor.execute("PRAGMA case_sensitive_like=TRUE")  # حساسية الأحرف في LIKE
    cursor.execute("PRAGMA secure_delete=FALSE")  # حذف سريع (أقل أماناً لكن أسرع)
    cursor.execute("PRAGMA auto_vacuum=INCREMENTAL")  # تنظيف تدريجي للمساحة

    cursor.close()

Base = declarative_base()
Session = sessionmaker(bind=engine)

# نموذج العملاء مع فهارس متقدمة
class Client(Base):
    __tablename__ = 'clients'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)  # فهرس على الاسم
    phone = Column(String(20), index=True)  # فهرس على الهاتف الرئيسي
    email = Column(String(100), index=True)  # فهرس على البريد الإلكتروني
    address = Column(String(200))
    balance = Column(Float, default=0.0, index=True)  # فهرس على الرصيد
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على التاريخ

    # العلاقات
    invoices = relationship("Invoice", back_populates="client")
    revenues = relationship("Revenue", back_populates="client")
    projects = relationship("Project", back_populates="client")
    documents = relationship("Document", back_populates="client")
    phones = relationship("ClientPhone", back_populates="client", cascade="all, delete-orphan")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_client_name_balance', 'name', 'balance'),  # فهرس مركب للاسم والرصيد
        Index('idx_client_created_balance', 'created_at', 'balance'),  # فهرس مركب للتاريخ والرصيد
        Index('idx_client_name_phone', 'name', 'phone'),  # فهرس مركب للاسم والهاتف
        Index('idx_client_email_name', 'email', 'name'),  # فهرس مركب للبريد والاسم
        UniqueConstraint('email', name='uq_client_email'),  # قيد فريد على البريد الإلكتروني
    )

    def __repr__(self):
        return f"<Client(id={self.id}, name='{self.name}', balance={self.balance})>"

# نموذج أرقام هواتف العملاء مع فهارس
class ClientPhone(Base):
    __tablename__ = 'client_phones'

    id = Column(Integer, primary_key=True)
    client_id = Column(Integer, ForeignKey('clients.id'), nullable=False, index=True)  # فهرس على العميل
    phone_number = Column(String(20), nullable=False, index=True)  # فهرس على رقم الهاتف
    label = Column(String(50), index=True)  # فهرس على التصنيف
    is_primary = Column(Boolean, default=False, index=True)  # فهرس على الرقم الرئيسي

    # العلاقات
    client = relationship("Client", back_populates="phones")

    # فهارس مركبة
    __table_args__ = (
        Index('idx_client_phone_client_primary', 'client_id', 'is_primary'),  # فهرس مركب للعميل والرئيسي
        UniqueConstraint('client_id', 'phone_number', name='uq_client_phone'),  # قيد فريد للعميل والرقم
    )

    def __repr__(self):
        return f"<ClientPhone(id={self.id}, client_id={self.client_id}, phone_number='{self.phone_number}')>"

# نموذج الموردين مع فهارس متقدمة
class Supplier(Base):
    __tablename__ = 'suppliers'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)  # فهرس على الاسم
    phone = Column(String(20), index=True)  # فهرس على الهاتف الرئيسي
    email = Column(String(100), index=True)  # فهرس على البريد الإلكتروني
    address = Column(String(200))
    balance = Column(Float, default=0.0, index=True)  # فهرس على الرصيد
    notes = Column(Text)
    status = Column(String(20), default='نشط', index=True)  # فهرس على الحالة
    created_at = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على التاريخ
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, index=True)

    # العلاقات
    expenses = relationship("Expense", back_populates="supplier")
    inventory_items = relationship("Inventory", back_populates="supplier")
    documents = relationship("Document", back_populates="supplier")
    phones = relationship("SupplierPhone", back_populates="supplier", cascade="all, delete-orphan")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_supplier_name_status', 'name', 'status'),  # فهرس مركب للاسم والحالة
        Index('idx_supplier_status_balance', 'status', 'balance'),  # فهرس مركب للحالة والرصيد
        Index('idx_supplier_created_updated', 'created_at', 'updated_at'),  # فهرس مركب للتواريخ
        Index('idx_supplier_name_balance', 'name', 'balance'),  # فهرس مركب للاسم والرصيد
        UniqueConstraint('email', name='uq_supplier_email'),  # قيد فريد على البريد الإلكتروني
    )

    def __repr__(self):
        return f"<Supplier(id={self.id}, name='{self.name}', balance={self.balance})>"

# نموذج أرقام هواتف الموردين مع فهارس
class SupplierPhone(Base):
    __tablename__ = 'supplier_phones'

    id = Column(Integer, primary_key=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, index=True)  # فهرس على المورد
    phone_number = Column(String(20), nullable=False, index=True)  # فهرس على رقم الهاتف
    label = Column(String(50), index=True)  # فهرس على التصنيف
    is_primary = Column(Boolean, default=False, index=True)  # فهرس على الرقم الرئيسي

    # العلاقات
    supplier = relationship("Supplier", back_populates="phones")

    # فهارس مركبة
    __table_args__ = (
        Index('idx_supplier_phone_supplier_primary', 'supplier_id', 'is_primary'),  # فهرس مركب للمورد والرئيسي
        UniqueConstraint('supplier_id', 'phone_number', name='uq_supplier_phone'),  # قيد فريد للمورد والرقم
    )

    def __repr__(self):
        return f"<SupplierPhone(id={self.id}, supplier_id={self.supplier_id}, phone_number='{self.phone_number}')>"

# نموذج الموظفين مع فهارس متقدمة
class Employee(Base):
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)  # فهرس على الاسم
    position = Column(String(100), index=True)  # فهرس على المنصب
    phone = Column(String(20), index=True)  # فهرس على الهاتف الرئيسي
    email = Column(String(100), index=True)  # فهرس على البريد الإلكتروني
    address = Column(String(200))
    hire_date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ التوظيف
    salary = Column(Float, default=0.0, index=True)  # فهرس على الراتب
    balance = Column(Float, default=0.0, index=True)  # فهرس على الرصيد
    notes = Column(Text)

    # العلاقات
    salaries = relationship("Salary", back_populates="employee")
    daily_wages = relationship("DailyWage", back_populates="employee")
    phones = relationship("EmployeePhone", back_populates="employee", cascade="all, delete-orphan")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_employee_name_position', 'name', 'position'),  # فهرس مركب للاسم والمنصب
        Index('idx_employee_hire_salary', 'hire_date', 'salary'),  # فهرس مركب للتوظيف والراتب
        Index('idx_employee_position_balance', 'position', 'balance'),  # فهرس مركب للمنصب والرصيد
        UniqueConstraint('email', name='uq_employee_email'),  # قيد فريد على البريد الإلكتروني
    )

    def __repr__(self):
        return f"<Employee(id={self.id}, name='{self.name}')>"

# نموذج أرقام هواتف الموظفين مع فهارس
class EmployeePhone(Base):
    __tablename__ = 'employee_phones'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False, index=True)  # فهرس على الموظف
    phone_number = Column(String(20), nullable=False, index=True)  # فهرس على رقم الهاتف
    label = Column(String(50), index=True)  # فهرس على التصنيف
    is_primary = Column(Boolean, default=False, index=True)  # فهرس على الرقم الرئيسي

    # العلاقات
    employee = relationship("Employee", back_populates="phones")

    # فهارس مركبة
    __table_args__ = (
        Index('idx_employee_phone_employee_primary', 'employee_id', 'is_primary'),  # فهرس مركب للموظف والرئيسي
        UniqueConstraint('employee_id', 'phone_number', name='uq_employee_phone'),  # قيد فريد للموظف والرقم
    )

    def __repr__(self):
        return f"<EmployeePhone(id={self.id}, employee_id={self.employee_id}, phone_number='{self.phone_number}')>"

# نموذج الرواتب مع فهارس متقدمة
class Salary(Base):
    __tablename__ = 'salaries'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), index=True)  # فهرس على الموظف
    amount = Column(Float, nullable=False, index=True)  # فهرس على المبلغ
    payment_date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الدفع
    month = Column(Integer, index=True)  # فهرس على الشهر
    year = Column(Integer, index=True)  # فهرس على السنة
    notes = Column(Text)

    # العلاقات
    employee = relationship("Employee", back_populates="salaries")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_salary_employee_date', 'employee_id', 'payment_date'),  # فهرس مركب للموظف والتاريخ
        Index('idx_salary_year_month', 'year', 'month'),  # فهرس مركب للسنة والشهر
        Index('idx_salary_employee_year_month', 'employee_id', 'year', 'month'),  # فهرس مركب شامل
        Index('idx_salary_amount_date', 'amount', 'payment_date'),  # فهرس مركب للمبلغ والتاريخ
        UniqueConstraint('employee_id', 'year', 'month', name='uq_salary_employee_period'),  # قيد فريد للفترة
    )

    def __repr__(self):
        return f"<Salary(id={self.id}, employee_id={self.employee_id}, amount={self.amount})>"

# نموذج اليوميات مع فهارس متقدمة
class DailyWage(Base):
    __tablename__ = 'daily_wages'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), index=True)  # فهرس على الموظف
    daily_amount = Column(Float, nullable=False, index=True)  # فهرس على المبلغ اليومي
    work_days = Column(Float, nullable=False, index=True)  # فهرس على أيام العمل
    total_amount = Column(Float, nullable=False, index=True)  # فهرس على المبلغ الإجمالي
    advance = Column(Float, default=0, index=True)  # فهرس على السُلف
    net_amount = Column(Float, nullable=False, index=True)  # فهرس على صافي المبلغ
    wage_date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ اليومية
    created_at = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الإنشاء

    # العلاقات
    employee = relationship("Employee", back_populates="daily_wages")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_daily_wage_employee_date', 'employee_id', 'wage_date'),  # فهرس مركب للموظف والتاريخ
        Index('idx_daily_wage_date_amount', 'wage_date', 'total_amount'),  # فهرس مركب للتاريخ والمبلغ
        Index('idx_daily_wage_advance_net', 'advance', 'net_amount'),  # فهرس مركب للسُلف والصافي
        Index('idx_daily_wage_work_days_amount', 'work_days', 'daily_amount'),  # فهرس مركب للأيام والمبلغ
    )

    def __repr__(self):
        return f"<DailyWage(id={self.id}, employee_id={self.employee_id}, daily_amount={self.daily_amount}, work_days={self.work_days})>"

# نموذج ملاحظات اليوميات
class DailyWageNote(Base):
    __tablename__ = 'daily_wage_notes'

    id = Column(Integer, primary_key=True)
    daily_wage_id = Column(Integer, ForeignKey('daily_wages.id'), index=True)  # فهرس على اليومية
    note_text = Column(Text, nullable=False)  # نص الملاحظة
    created_at = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الإنشاء

    # العلاقات
    daily_wage = relationship("DailyWage", backref="notes")

    # فهارس مركبة
    __table_args__ = (
        Index('idx_daily_wage_note_wage_date', 'daily_wage_id', 'created_at'),  # فهرس مركب لليومية والتاريخ
    )

    def __repr__(self):
        return f"<DailyWageNote(id={self.id}, daily_wage_id={self.daily_wage_id})>"

# نموذج المصروفات مع فهارس متقدمة
class Expense(Base):
    __tablename__ = 'expenses'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False, index=True)  # فهرس على العنوان
    amount = Column(Float, nullable=False, index=True)  # فهرس على المبلغ
    date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على التاريخ
    category = Column(String(100), index=True)  # فهرس على الفئة
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True, index=True)  # فهرس على المورد
    notes = Column(Text)

    # العلاقات
    supplier = relationship("Supplier", back_populates="expenses")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_expense_date_amount', 'date', 'amount'),  # فهرس مركب للتاريخ والمبلغ
        Index('idx_expense_category_date', 'category', 'date'),  # فهرس مركب للفئة والتاريخ
        Index('idx_expense_supplier_date', 'supplier_id', 'date'),  # فهرس مركب للمورد والتاريخ
        Index('idx_expense_amount_category', 'amount', 'category'),  # فهرس مركب للمبلغ والفئة
    )

    def __repr__(self):
        return f"<Expense(id={self.id}, title='{self.title}', amount={self.amount})>"

# نموذج الإيرادات مع فهارس متقدمة
class Revenue(Base):
    __tablename__ = 'revenues'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False, index=True)  # فهرس على العنوان
    amount = Column(Float, nullable=False, index=True)  # فهرس على المبلغ
    date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على التاريخ
    category = Column(String(100), index=True)  # فهرس على الفئة
    client_id = Column(Integer, ForeignKey('clients.id'), nullable=True, index=True)  # فهرس على العميل
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=True, index=True)  # فهرس على الفاتورة
    notes = Column(Text)

    # العلاقات
    client = relationship("Client", back_populates="revenues")
    invoice = relationship("Invoice", back_populates="revenues")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_revenue_date_amount', 'date', 'amount'),  # فهرس مركب للتاريخ والمبلغ
        Index('idx_revenue_category_date', 'category', 'date'),  # فهرس مركب للفئة والتاريخ
        Index('idx_revenue_client_date', 'client_id', 'date'),  # فهرس مركب للعميل والتاريخ
        Index('idx_revenue_invoice_amount', 'invoice_id', 'amount'),  # فهرس مركب للفاتورة والمبلغ
    )

    def __repr__(self):
        return f"<Revenue(id={self.id}, title='{self.title}', amount={self.amount})>"

# نموذج الفواتير مع فهارس متقدمة
class Invoice(Base):
    __tablename__ = 'invoices'

    id = Column(Integer, primary_key=True)
    invoice_number = Column(String(50), unique=True, index=True)  # فهرس فريد على رقم الفاتورة
    client_id = Column(Integer, ForeignKey('clients.id'), index=True)  # فهرس على معرف العميل
    date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على التاريخ
    due_date = Column(DateTime, index=True)  # فهرس على تاريخ الاستحقاق
    total_amount = Column(Float, default=0.0, index=True)  # فهرس على المبلغ الإجمالي
    paid_amount = Column(Float, default=0.0, index=True)  # فهرس على المبلغ المدفوع
    status = Column(String(20), default='pending', index=True)  # فهرس على الحالة
    notes = Column(Text)

    # العلاقات
    client = relationship("Client", back_populates="invoices")
    items = relationship("InvoiceItem", back_populates="invoice", cascade="all, delete-orphan")
    revenues = relationship("Revenue", back_populates="invoice")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_invoice_client_date', 'client_id', 'date'),  # فهرس مركب للعميل والتاريخ
        Index('idx_invoice_status_date', 'status', 'date'),  # فهرس مركب للحالة والتاريخ
        Index('idx_invoice_due_status', 'due_date', 'status'),  # فهرس مركب لتاريخ الاستحقاق والحالة
        Index('idx_invoice_amount_status', 'total_amount', 'status'),  # فهرس مركب للمبلغ والحالة
        Index('idx_invoice_client_status', 'client_id', 'status'),  # فهرس مركب للعميل والحالة
        Index('idx_invoice_date_amount', 'date', 'total_amount'),  # فهرس مركب للتاريخ والمبلغ
    )

    def __repr__(self):
        return f"<Invoice(id={self.id}, invoice_number='{self.invoice_number}', total_amount={self.total_amount})>"

# نموذج عناصر الفاتورة مع فهارس
class InvoiceItem(Base):
    __tablename__ = 'invoice_items'

    id = Column(Integer, primary_key=True)
    invoice_id = Column(Integer, ForeignKey('invoices.id'), index=True)  # فهرس على الفاتورة
    description = Column(String(200), nullable=False, index=True)  # فهرس على الوصف
    quantity = Column(Float, default=1.0, index=True)  # فهرس على الكمية
    unit_price = Column(Float, nullable=False, index=True)  # فهرس على سعر الوحدة
    total_price = Column(Float, nullable=False, index=True)  # فهرس على السعر الإجمالي

    # العلاقات
    invoice = relationship("Invoice", back_populates="items")

    # فهارس مركبة
    __table_args__ = (
        Index('idx_invoice_item_invoice_price', 'invoice_id', 'total_price'),  # فهرس مركب للفاتورة والسعر
        Index('idx_invoice_item_quantity_price', 'quantity', 'unit_price'),  # فهرس مركب للكمية والسعر
    )

    def __repr__(self):
        return f"<InvoiceItem(id={self.id}, description='{self.description}', total_price={self.total_price})>"

# نموذج الإشعارات مع فهارس متقدمة
class Notification(Base):
    __tablename__ = 'notifications'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False, index=True)  # فهرس على العنوان
    message = Column(Text, nullable=False)
    date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على التاريخ
    is_read = Column(Boolean, default=False, index=True)  # فهرس على حالة القراءة
    type = Column(String(50), index=True)  # فهرس على النوع
    related_id = Column(Integer, index=True)  # فهرس على المعرف المرتبط

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_notification_type_date', 'type', 'date'),  # فهرس مركب للنوع والتاريخ
        Index('idx_notification_read_date', 'is_read', 'date'),  # فهرس مركب للقراءة والتاريخ
        Index('idx_notification_type_read', 'type', 'is_read'),  # فهرس مركب للنوع والقراءة
        Index('idx_notification_related_type', 'related_id', 'type'),  # فهرس مركب للمعرف والنوع
    )

    def __repr__(self):
        return f"<Notification(id={self.id}, title='{self.title}', is_read={self.is_read})>"

# نموذج التنبيهات (للتذكير)
class Reminder(Base):
    __tablename__ = 'reminders'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False, index=True)  # فهرس على العنوان
    description = Column(Text)
    reminder_date = Column(DateTime, nullable=False, index=True)  # فهرس على تاريخ التنبيه
    created_date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الإنشاء
    is_completed = Column(Boolean, default=False, index=True)  # فهرس على حالة الإكمال
    priority = Column(String(20), default='medium', index=True)  # فهرس على الأولوية
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, index=True)  # فهرس على المستخدم

    # العلاقات
    user = relationship("User")
    events = relationship("Event", back_populates="reminder", cascade="all, delete-orphan")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_reminder_date_priority', 'reminder_date', 'priority'),  # فهرس مركب للتاريخ والأولوية
        Index('idx_reminder_completed_date', 'is_completed', 'reminder_date'),  # فهرس مركب للإكمال والتاريخ
        Index('idx_reminder_user_priority', 'user_id', 'priority'),  # فهرس مركب للمستخدم والأولوية
        Index('idx_reminder_priority_completed', 'priority', 'is_completed'),  # فهرس مركب للأولوية والإكمال
        Index('idx_reminder_created_completed', 'created_date', 'is_completed'),  # فهرس مركب للإنشاء والإكمال
    )

    def __repr__(self):
        return f"<Reminder(id={self.id}, title='{self.title}', reminder_date='{self.reminder_date}')>"

# نموذج الأحداث المرتبطة بالتنبيهات
class Event(Base):
    __tablename__ = 'events'

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False, index=True)  # فهرس على العنوان
    description = Column(Text)
    event_date = Column(DateTime, nullable=False, index=True)  # فهرس على تاريخ الحدث
    created_date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الإنشاء
    reminder_id = Column(Integer, ForeignKey('reminders.id'), nullable=False, index=True)  # فهرس على التنبيه
    status = Column(String(20), default='pending', index=True)  # فهرس على الحالة
    notes = Column(Text)

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_event_reminder_date', 'reminder_id', 'event_date'),  # فهرس مركب للتنبيه والتاريخ
        Index('idx_event_status_date', 'status', 'event_date'),  # فهرس مركب للحالة والتاريخ
        Index('idx_event_date_status', 'event_date', 'status'),  # فهرس مركب للتاريخ والحالة
    )

    # العلاقات
    reminder = relationship("Reminder", back_populates="events")

    def __repr__(self):
        return f"<Event(id={self.id}, title='{self.title}', event_date='{self.event_date}')>"

# نموذج المشاريع
class Project(Base):
    __tablename__ = 'projects'

    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False, index=True)  # فهرس على اسم المشروع
    client_id = Column(Integer, ForeignKey('clients.id'), index=True)  # فهرس على العميل
    location = Column(String(200), index=True)  # فهرس على الموقع
    area = Column(Float, index=True)  # فهرس على المساحة
    start_date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ البداية
    expected_end_date = Column(DateTime, index=True)  # فهرس على التاريخ المتوقع
    actual_end_date = Column(DateTime, index=True)  # فهرس على التاريخ الفعلي
    status = Column(String(50), default='planning', index=True)  # فهرس على الحالة
    budget = Column(Float, default=0.0, index=True)  # فهرس على الميزانية
    total_cost = Column(Float, default=0.0, index=True)  # فهرس على التكلفة الإجمالية
    description = Column(Text)
    notes = Column(Text)

    # العلاقات
    client = relationship("Client")
    expenses = relationship("ProjectExpense", back_populates="project", cascade="all, delete-orphan")
    materials = relationship("ProjectMaterial", back_populates="project", cascade="all, delete-orphan")
    documents = relationship("Document", back_populates="project", cascade="all, delete-orphan")
    properties = relationship("Property", back_populates="project", cascade="all, delete-orphan")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_project_client_status', 'client_id', 'status'),  # فهرس مركب للعميل والحالة
        Index('idx_project_status_date', 'status', 'start_date'),  # فهرس مركب للحالة والتاريخ
        Index('idx_project_budget_cost', 'budget', 'total_cost'),  # فهرس مركب للميزانية والتكلفة
        Index('idx_project_location_status', 'location', 'status'),  # فهرس مركب للموقع والحالة
        Index('idx_project_date_range', 'start_date', 'expected_end_date'),  # فهرس مركب لفترة المشروع
        Index('idx_project_area_location', 'area', 'location'),  # فهرس مركب للمساحة والموقع
    )

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', status='{self.status}')>"

# نموذج الأراضي والشقق
class Property(Base):
    __tablename__ = 'properties'

    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False, index=True)  # فهرس على العنوان
    type = Column(String(50), nullable=False, index=True)  # فهرس على النوع
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=True, index=True)  # فهرس على المشروع
    location = Column(String(200), index=True)  # فهرس على الموقع
    area = Column(Float, index=True)  # فهرس على المساحة
    price = Column(Float, default=0.0, index=True)  # فهرس على السعر
    status = Column(String(50), default='available', index=True)  # فهرس على الحالة
    description = Column(Text)
    features = Column(Text)  # مميزات العقار
    created_at = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الإنشاء
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, index=True)

    # العلاقات
    project = relationship("Project", back_populates="properties")
    documents = relationship("PropertyDocument", back_populates="property", cascade="all, delete-orphan")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_property_type_status', 'type', 'status'),  # فهرس مركب للنوع والحالة
        Index('idx_property_location_type', 'location', 'type'),  # فهرس مركب للموقع والنوع
        Index('idx_property_price_area', 'price', 'area'),  # فهرس مركب للسعر والمساحة
        Index('idx_property_status_price', 'status', 'price'),  # فهرس مركب للحالة والسعر
        Index('idx_property_project_status', 'project_id', 'status'),  # فهرس مركب للمشروع والحالة
        Index('idx_property_area_location', 'area', 'location'),  # فهرس مركب للمساحة والموقع
    )

    def __repr__(self):
        return f"<Property(id={self.id}, title='{self.title}', type='{self.type}', price={self.price})>"

# نموذج وثائق وصور العقارات
class PropertyDocument(Base):
    __tablename__ = 'property_documents'

    id = Column(Integer, primary_key=True)
    property_id = Column(Integer, ForeignKey('properties.id'), nullable=False)
    title = Column(String(200), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_type = Column(String(50))  # image, pdf, doc, etc.
    is_main_image = Column(Boolean, default=False)  # هل هي الصورة الرئيسية للعقار
    upload_date = Column(DateTime, default=datetime.datetime.now)
    description = Column(Text)

    # العلاقات
    property = relationship("Property", back_populates="documents")

    def __repr__(self):
        return f"<PropertyDocument(id={self.id}, title='{self.title}', property_id={self.property_id})>"

# نموذج مصروفات المشروع
class ProjectExpense(Base):
    __tablename__ = 'project_expenses'

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, ForeignKey('projects.id'))
    expense_id = Column(Integer, ForeignKey('expenses.id'))
    amount = Column(Float, nullable=False)
    date = Column(DateTime, default=datetime.datetime.now)
    notes = Column(Text)

    # العلاقات
    project = relationship("Project", back_populates="expenses")
    expense = relationship("Expense")

    def __repr__(self):
        return f"<ProjectExpense(id={self.id}, project_id={self.project_id}, amount={self.amount})>"

# نموذج المخزون مع فهارس متقدمة
class Inventory(Base):
    __tablename__ = 'inventory'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)  # فهرس على اسم الصنف
    category = Column(String(100), index=True)  # فهرس على الفئة
    unit = Column(String(50))  # وحدة القياس (متر، كيلوجرام، قطعة، إلخ)
    quantity = Column(Float, default=0.0, index=True)  # فهرس على الكمية
    min_quantity = Column(Float, default=0.0, index=True)  # فهرس على الحد الأدنى
    cost_price = Column(Float, default=0.0, index=True)  # فهرس على سعر التكلفة
    selling_price = Column(Float, default=0.0, index=True)  # فهرس على سعر البيع
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True, index=True)  # فهرس على المورد
    location = Column(String(100), index=True)  # فهرس على موقع التخزين
    notes = Column(Text)
    last_updated = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على آخر تحديث

    # العلاقات
    supplier = relationship("Supplier", back_populates="inventory_items")
    project_materials = relationship("ProjectMaterial", back_populates="material")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_inventory_name_category', 'name', 'category'),  # فهرس مركب للاسم والفئة
        Index('idx_inventory_quantity_min', 'quantity', 'min_quantity'),  # فهرس مركب للكمية والحد الأدنى
        Index('idx_inventory_supplier_category', 'supplier_id', 'category'),  # فهرس مركب للمورد والفئة
        Index('idx_inventory_location_category', 'location', 'category'),  # فهرس مركب للموقع والفئة
        Index('idx_inventory_price_range', 'cost_price', 'selling_price'),  # فهرس مركب للأسعار
        Index('idx_inventory_updated_quantity', 'last_updated', 'quantity'),  # فهرس مركب للتحديث والكمية
        UniqueConstraint('name', 'supplier_id', name='uq_inventory_name_supplier'),  # قيد فريد للاسم والمورد
    )

    def __repr__(self):
        return f"<Inventory(id={self.id}, name='{self.name}', quantity={self.quantity})>"

# نموذج المشتريات
class Purchase(Base):
    __tablename__ = 'purchases'

    id = Column(Integer, primary_key=True)
    purchase_number = Column(String(50), unique=True, index=True)  # فهرس فريد على رقم المشتريات
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), index=True)  # فهرس على المورد
    date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على التاريخ
    total_amount = Column(Float, default=0.0, index=True)  # فهرس على المبلغ الإجمالي
    paid_amount = Column(Float, default=0.0, index=True)  # فهرس على المبلغ المدفوع
    status = Column(String(20), default='pending', index=True)  # فهرس على الحالة
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الإنشاء

    # العلاقات
    supplier = relationship("Supplier")
    items = relationship("PurchaseItem", back_populates="purchase", cascade="all, delete-orphan")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_purchase_supplier_date', 'supplier_id', 'date'),  # فهرس مركب للمورد والتاريخ
        Index('idx_purchase_status_date', 'status', 'date'),  # فهرس مركب للحالة والتاريخ
        Index('idx_purchase_amount_status', 'total_amount', 'status'),  # فهرس مركب للمبلغ والحالة
        Index('idx_purchase_date_amount', 'date', 'total_amount'),  # فهرس مركب للتاريخ والمبلغ
        Index('idx_purchase_created_status', 'created_at', 'status'),  # فهرس مركب للإنشاء والحالة
    )

    def __repr__(self):
        return f"<Purchase(id={self.id}, purchase_number='{self.purchase_number}', total_amount={self.total_amount})>"

# نموذج عناصر المشتريات
class PurchaseItem(Base):
    __tablename__ = 'purchase_items'

    id = Column(Integer, primary_key=True)
    purchase_id = Column(Integer, ForeignKey('purchases.id'), index=True)  # فهرس على المشتريات
    inventory_id = Column(Integer, ForeignKey('inventory.id'), index=True)  # فهرس على المخزون
    quantity = Column(Float, nullable=False, index=True)  # فهرس على الكمية
    unit_price = Column(Float, nullable=False, index=True)  # فهرس على سعر الوحدة
    total_price = Column(Float, nullable=False, index=True)  # فهرس على السعر الإجمالي
    received_quantity = Column(Float, default=0.0, index=True)  # فهرس على الكمية المستلمة
    notes = Column(Text)

    # فهارس مركبة
    __table_args__ = (
        Index('idx_purchase_item_purchase_inventory', 'purchase_id', 'inventory_id'),  # فهرس مركب للمشتريات والمخزون
        Index('idx_purchase_item_inventory_price', 'inventory_id', 'unit_price'),  # فهرس مركب للمخزون والسعر
        Index('idx_purchase_item_quantity_received', 'quantity', 'received_quantity'),  # فهرس مركب للكمية والمستلمة
    )

    # العلاقات
    purchase = relationship("Purchase", back_populates="items")
    inventory_item = relationship("Inventory")

    def __repr__(self):
        return f"<PurchaseItem(id={self.id}, purchase_id={self.purchase_id}, inventory_id={self.inventory_id}, quantity={self.quantity})>"

# نموذج المبيعات
class Sale(Base):
    __tablename__ = 'sales'

    id = Column(Integer, primary_key=True)
    sale_number = Column(String(50), unique=True, index=True)  # فهرس فريد على رقم المبيعات
    client_id = Column(Integer, ForeignKey('clients.id'), index=True)  # فهرس على العميل
    date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على التاريخ
    total_amount = Column(Float, default=0.0, index=True)  # فهرس على المبلغ الإجمالي
    paid_amount = Column(Float, default=0.0, index=True)  # فهرس على المبلغ المدفوع
    discount_amount = Column(Float, default=0.0, index=True)  # فهرس على الخصم
    tax_amount = Column(Float, default=0.0, index=True)  # فهرس على الضريبة
    status = Column(String(20), default='pending', index=True)  # فهرس على الحالة
    payment_method = Column(String(50), default='cash', index=True)  # فهرس على طريقة الدفع
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الإنشاء

    # العلاقات
    client = relationship("Client")
    items = relationship("SaleItem", back_populates="sale", cascade="all, delete-orphan")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_sale_client_date', 'client_id', 'date'),  # فهرس مركب للعميل والتاريخ
        Index('idx_sale_status_date', 'status', 'date'),  # فهرس مركب للحالة والتاريخ
        Index('idx_sale_amount_status', 'total_amount', 'status'),  # فهرس مركب للمبلغ والحالة
        Index('idx_sale_payment_method_date', 'payment_method', 'date'),  # فهرس مركب لطريقة الدفع والتاريخ
        Index('idx_sale_date_amount', 'date', 'total_amount'),  # فهرس مركب للتاريخ والمبلغ
        Index('idx_sale_discount_tax', 'discount_amount', 'tax_amount'),  # فهرس مركب للخصم والضريبة
    )

    def __repr__(self):
        return f"<Sale(id={self.id}, sale_number='{self.sale_number}', total_amount={self.total_amount})>"

# نموذج عناصر المبيعات
class SaleItem(Base):
    __tablename__ = 'sale_items'

    id = Column(Integer, primary_key=True)
    sale_id = Column(Integer, ForeignKey('sales.id'), index=True)  # فهرس على المبيعات
    inventory_id = Column(Integer, ForeignKey('inventory.id'), index=True)  # فهرس على المخزون
    quantity = Column(Float, nullable=False, index=True)  # فهرس على الكمية
    unit_price = Column(Float, nullable=False, index=True)  # فهرس على سعر الوحدة
    total_price = Column(Float, nullable=False, index=True)  # فهرس على السعر الإجمالي
    discount_amount = Column(Float, default=0.0, index=True)  # فهرس على الخصم
    notes = Column(Text)

    # فهارس مركبة
    __table_args__ = (
        Index('idx_sale_item_sale_inventory', 'sale_id', 'inventory_id'),  # فهرس مركب للمبيعات والمخزون
        Index('idx_sale_item_inventory_price', 'inventory_id', 'unit_price'),  # فهرس مركب للمخزون والسعر
        Index('idx_sale_item_quantity_discount', 'quantity', 'discount_amount'),  # فهرس مركب للكمية والخصم
        Index('idx_sale_item_total_discount', 'total_price', 'discount_amount'),  # فهرس مركب للإجمالي والخصم
    )

    # العلاقات
    sale = relationship("Sale", back_populates="items")
    inventory_item = relationship("Inventory")

    def __repr__(self):
        return f"<SaleItem(id={self.id}, sale_id={self.sale_id}, inventory_id={self.inventory_id}, quantity={self.quantity})>"

# نموذج المواد المستخدمة في المشروع
class ProjectMaterial(Base):
    __tablename__ = 'project_materials'

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, ForeignKey('projects.id'))
    material_id = Column(Integer, ForeignKey('inventory.id'))
    quantity = Column(Float, nullable=False)
    unit_price = Column(Float, nullable=False)
    total_price = Column(Float, nullable=False)
    date_used = Column(DateTime, default=datetime.datetime.now)
    notes = Column(Text)

    # العلاقات
    project = relationship("Project", back_populates="materials")
    material = relationship("Inventory", back_populates="project_materials")

    def __repr__(self):
        return f"<ProjectMaterial(id={self.id}, project_id={self.project_id}, material_id={self.material_id}, quantity={self.quantity})>"

# نموذج الوثائق والصور
class Document(Base):
    __tablename__ = 'documents'

    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False, index=True)  # فهرس على العنوان
    file_path = Column(String(500), nullable=False)
    file_type = Column(String(50), index=True)  # فهرس على نوع الملف
    upload_date = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الرفع
    description = Column(Text)
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=True, index=True)  # فهرس على المشروع
    client_id = Column(Integer, ForeignKey('clients.id'), nullable=True, index=True)  # فهرس على العميل
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True, index=True)  # فهرس على المورد

    # العلاقات
    project = relationship("Project", back_populates="documents")
    client = relationship("Client", back_populates="documents")
    supplier = relationship("Supplier", back_populates="documents")

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_document_type_date', 'file_type', 'upload_date'),  # فهرس مركب للنوع والتاريخ
        Index('idx_document_project_type', 'project_id', 'file_type'),  # فهرس مركب للمشروع والنوع
        Index('idx_document_client_type', 'client_id', 'file_type'),  # فهرس مركب للعميل والنوع
        Index('idx_document_supplier_type', 'supplier_id', 'file_type'),  # فهرس مركب للمورد والنوع
        Index('idx_document_date_title', 'upload_date', 'title'),  # فهرس مركب للتاريخ والعنوان
    )

    def __repr__(self):
        return f"<Document(id={self.id}, title='{self.title}', file_type='{self.file_type}')>"

# نموذج الإعدادات
class Setting(Base):
    __tablename__ = 'settings'

    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False, index=True)  # فهرس فريد على المفتاح
    value = Column(String(500))
    category = Column(String(100), index=True)  # فهرس على الفئة
    description = Column(String(200))

    # فهارس مركبة متقدمة
    __table_args__ = (
        Index('idx_setting_category_key', 'category', 'key'),  # فهرس مركب للفئة والمفتاح
    )

    def __repr__(self):
        return f"<Setting(key='{self.key}', value='{self.value}')>"

# نموذج المستخدمين
class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False, index=True)  # فهرس فريد على اسم المستخدم
    password = Column(String(100), nullable=False)  # سيتم تخزين كلمة المرور بشكل مشفر
    full_name = Column(String(100), index=True)  # فهرس على الاسم الكامل
    email = Column(String(100), index=True)  # فهرس على البريد الإلكتروني
    phone = Column(String(20), index=True)  # فهرس على الهاتف
    role = Column(String(20), default='user', index=True)  # فهرس على الدور
    is_active = Column(Boolean, default=True, index=True)  # فهرس على الحالة النشطة
    last_login = Column(DateTime, index=True)  # فهرس على آخر تسجيل دخول
    created_at = Column(DateTime, default=datetime.datetime.now, index=True)  # فهرس على تاريخ الإنشاء

    # صلاحيات المستخدم
    can_view_dashboard = Column(Boolean, default=True)
    can_manage_clients = Column(Boolean, default=False)
    can_manage_suppliers = Column(Boolean, default=False)
    can_manage_employees = Column(Boolean, default=False)
    can_manage_projects = Column(Boolean, default=False)
    can_manage_inventory = Column(Boolean, default=False)
    can_manage_expenses = Column(Boolean, default=False)
    can_manage_revenues = Column(Boolean, default=False)
    can_manage_invoices = Column(Boolean, default=False)
    can_view_reports = Column(Boolean, default=False)
    can_manage_settings = Column(Boolean, default=False)
    can_manage_users = Column(Boolean, default=False)

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"

    def set_permissions_by_role(self):
        """تعيين الصلاحيات بناءً على دور المستخدم"""
        if self.role == 'admin':
            # المدير لديه جميع الصلاحيات
            self.can_view_dashboard = True
            self.can_manage_clients = True
            self.can_manage_suppliers = True
            self.can_manage_employees = True
            self.can_manage_projects = True
            self.can_manage_inventory = True
            self.can_manage_expenses = True
            self.can_manage_revenues = True
            self.can_manage_invoices = True
            self.can_view_reports = True
            self.can_manage_settings = True
            self.can_manage_users = True
        elif self.role == 'manager':
            # المدير التنفيذي لديه معظم الصلاحيات ما عدا إدارة المستخدمين والإعدادات
            self.can_view_dashboard = True
            self.can_manage_clients = True
            self.can_manage_suppliers = True
            self.can_manage_employees = True
            self.can_manage_projects = True
            self.can_manage_inventory = True
            self.can_manage_expenses = True
            self.can_manage_revenues = True
            self.can_manage_invoices = True
            self.can_view_reports = True
            self.can_manage_settings = False
            self.can_manage_users = False
        elif self.role == 'accountant':
            # المحاسب لديه صلاحيات محدودة تتعلق بالمالية
            self.can_view_dashboard = True
            self.can_manage_clients = True
            self.can_manage_suppliers = True
            self.can_manage_employees = False
            self.can_manage_projects = False
            self.can_manage_inventory = False
            self.can_manage_expenses = True
            self.can_manage_revenues = True
            self.can_manage_invoices = True
            self.can_view_reports = True
            self.can_manage_settings = False
            self.can_manage_users = False
        else:  # user
            # المستخدم العادي لديه صلاحيات محدودة جداً
            self.can_view_dashboard = True
            self.can_manage_clients = False
            self.can_manage_suppliers = False
            self.can_manage_employees = False
            self.can_manage_projects = False
            self.can_manage_inventory = False
            self.can_manage_expenses = False
            self.can_manage_revenues = False
            self.can_manage_invoices = False
            self.can_view_reports = False
            self.can_manage_settings = False
            self.can_manage_users = False

# إنشاء جميع الجداول في قاعدة البيانات مع التحسينات
def init_db():
    """تهيئة قاعدة البيانات مع التحسينات المتقدمة"""
    # إنشاء الجداول الأساسية
    Base.metadata.create_all(engine)

    # تطبيق التحسينات المتقدمة الشاملة
    try:
        session = get_session()

        # إنشاء فهارس الأداء العامة
        create_performance_indexes(session)

        # تحسين أداء لوحة المعلومات
        optimize_dashboard_performance(session)

        # تحسين أداء التقارير
        optimize_reports_performance(session)

        # تحسين قاعدة البيانات العام
        optimize_database(session)

        session.close()
        print("✅ تم تطبيق جميع تحسينات قاعدة البيانات المتقدمة")
    except Exception as e:
        print(f"⚠️ تحذير: فشل في تطبيق بعض التحسينات: {e}")

    # إضافة الإعدادات الافتراضية إذا لم تكن موجودة
    session = Session()

    default_settings = [
        # إعدادات عامة
        {'key': 'company_name', 'value': 'Smart Finish', 'category': 'general', 'description': 'اسم الشركة'},
        {'key': 'company_phone', 'value': '', 'category': 'general', 'description': 'رقم هاتف الشركة'},
        {'key': 'company_email', 'value': '', 'category': 'general', 'description': 'البريد الإلكتروني للشركة'},
        {'key': 'company_address', 'value': '', 'category': 'general', 'description': 'عنوان الشركة'},
        {'key': 'company_logo', 'value': 'resources/logo.png', 'category': 'general', 'description': 'شعار الشركة'},
        {'key': 'company_subtitle', 'value': 'إدارة / محمد جمال', 'category': 'general', 'description': 'العنوان الجانبي للشركة'},

        # إعدادات مالية
        {'key': 'currency', 'value': 'جنية', 'category': 'financial', 'description': 'العملة'},
        {'key': 'tax_rate', 'value': '15', 'category': 'financial', 'description': 'نسبة الضريبة (%)'},
        {'key': 'invoice_prefix', 'value': 'INV-', 'category': 'financial', 'description': 'بادئة رقم الفاتورة'},
        {'key': 'invoice_due_days', 'value': '30', 'category': 'financial', 'description': 'عدد أيام استحقاق الفاتورة الافتراضي'},

        # إعدادات الإشعارات
        {'key': 'enable_notifications', 'value': 'true', 'category': 'notifications', 'description': 'تفعيل الإشعارات'},
        {'key': 'notify_low_stock', 'value': 'true', 'category': 'notifications', 'description': 'إشعار عند انخفاض المخزون'},
        {'key': 'notify_due_invoices', 'value': 'true', 'category': 'notifications', 'description': 'إشعار عند استحقاق الفواتير'},

        # إعدادات النسخ الاحتياطي
        {'key': 'auto_backup', 'value': 'false', 'category': 'backup', 'description': 'نسخ احتياطي تلقائي'},
        {'key': 'backup_frequency', 'value': 'weekly', 'category': 'backup', 'description': 'تكرار النسخ الاحتياطي (daily/weekly/monthly)'},
        {'key': 'backup_path', 'value': 'backups', 'category': 'backup', 'description': 'مسار النسخ الاحتياطي'}
    ]

    for setting_data in default_settings:
        # التحقق مما إذا كان الإعداد موجودًا بالفعل
        existing = session.query(Setting).filter_by(key=setting_data['key']).first()
        if not existing:
            setting = Setting(**setting_data)
            session.add(setting)

    try:
        session.commit()

        # إضافة مستخدم افتراضي إذا لم يكن هناك مستخدمين
        user_count = session.query(User).count()
        if user_count == 0:
            # إنشاء مستخدم افتراضي (admin/admin)
            default_admin = User(
                username="admin",
                password=hash_password("admin"),
                full_name="مدير النظام",
                role="admin",
                is_active=True
            )
            session.add(default_admin)
            session.commit()
            # تم إنشاء مستخدم افتراضي بصمت
    except Exception as e:
        session.rollback()
        # خطأ في تهيئة قاعدة البيانات (تم إخفاء الرسالة)
    finally:
        session.close()

# الحصول على جلسة قاعدة البيانات
def get_session():
    """إنشاء جلسة جديدة مع تحسينات الأداء"""
    session = Session()
    # تعيين مهلة انتهاء الجلسة لتجنب مشاكل التجمد
    session.execute(text("PRAGMA busy_timeout=30000"))  # 30 ثانية
    return session

# الحصول على قيمة إعداد معين
def get_setting(session, key, default=None):
    setting = session.query(Setting).filter_by(key=key).first()
    if setting:
        return setting.value
    return default

# تشفير كلمة المرور
def hash_password(password):
    """تشفير كلمة المرور باستخدام خوارزمية bcrypt"""
    import hashlib
    # استخدام SHA-256 لتشفير كلمة المرور (في الإنتاج يفضل استخدام bcrypt)
    return hashlib.sha256(password.encode()).hexdigest()

# التحقق من صحة كلمة المرور
def verify_password(stored_password, provided_password):
    """التحقق من صحة كلمة المرور"""
    import hashlib
    hashed_provided = hashlib.sha256(provided_password.encode()).hexdigest()
    return stored_password == hashed_provided

# إضافة مستخدم جديد
def add_user(session, username, password, full_name='', email='', phone='', role='user'):
    """إضافة مستخدم جديد إلى قاعدة البيانات"""
    # التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    existing_user = session.query(User).filter_by(username=username).first()
    if existing_user:
        return False, "اسم المستخدم موجود بالفعل"

    # تشفير كلمة المرور
    hashed_password = hash_password(password)

    # إنشاء مستخدم جديد
    user = User(
        username=username,
        password=hashed_password,
        full_name=full_name,
        email=email,
        phone=phone,
        role=role
    )

    # تعيين الصلاحيات بناءً على الدور
    user.set_permissions_by_role()

    # إضافة المستخدم إلى قاعدة البيانات
    try:
        session.add(user)
        session.commit()
        return True, "تم إضافة المستخدم بنجاح"
    except Exception as e:
        session.rollback()
        return False, f"حدث خطأ أثناء إضافة المستخدم: {str(e)}"

# التحقق من صحة بيانات الدخول
def authenticate_user(session, username, password):
    """التحقق من صحة بيانات الدخول"""
    # البحث عن المستخدم
    user = session.query(User).filter_by(username=username).first()
    if not user:
        return None, "اسم المستخدم غير موجود"

    # التحقق من كلمة المرور
    if not verify_password(user.password, password):
        return None, "كلمة المرور غير صحيحة"

    # التحقق من حالة المستخدم
    if not user.is_active:
        return None, "الحساب غير نشط"

    # تحديث وقت آخر تسجيل دخول
    user.last_login = datetime.datetime.now()
    session.commit()

    return user, "تم تسجيل الدخول بنجاح"

# تغيير كلمة المرور
def change_password(session, user_id, old_password, new_password):
    """تغيير كلمة المرور للمستخدم"""
    # البحث عن المستخدم
    user = session.query(User).filter_by(id=user_id).first()
    if not user:
        return False, "المستخدم غير موجود"

    # التحقق من كلمة المرور القديمة
    if not verify_password(user.password, old_password):
        return False, "كلمة المرور القديمة غير صحيحة"

    # تشفير كلمة المرور الجديدة
    hashed_password = hash_password(new_password)

    # تحديث كلمة المرور
    try:
        user.password = hashed_password
        session.commit()
        return True, "تم تغيير كلمة المرور بنجاح"
    except Exception as e:
        session.rollback()
        return False, f"حدث خطأ أثناء تغيير كلمة المرور: {str(e)}"

# تحديث قيمة إعداد معين
def update_setting(session, key, value):
    setting = session.query(Setting).filter_by(key=key).first()
    if setting:
        setting.value = value
        session.commit()
        return True
    return False

# تعيين قيمة إعداد (إنشاء إذا لم يكن موجودًا أو تحديث إذا كان موجودًا)
def set_setting(session, key, value):
    setting = session.query(Setting).filter_by(key=key).first()
    if setting:
        setting.value = value
    else:
        setting = Setting(key=key, value=value)
        session.add(setting)
    session.commit()
    return True

# تحديث رصيد العميل
def update_client_balance(session, client_id, amount, operation='add'):
    """
    تحديث رصيد العميل

    المعاملات:
    - session: جلسة قاعدة البيانات
    - client_id: معرف العميل
    - amount: المبلغ المراد إضافته أو خصمه
    - operation: نوع العملية ('add' للإضافة، 'subtract' للخصم)

    ملاحظة: القيمة الموجبة تعني أن المبلغ للعميل، والقيمة السالبة تعني أن المبلغ على العميل
    """
    client = session.query(Client).filter_by(id=client_id).first()
    if client:
        if operation == 'add':
            client.balance += amount
        elif operation == 'subtract':
            client.balance -= amount
        session.commit()
        return True, client.balance
    return False, 0

# تحديث رصيد المورد
def update_supplier_balance(session, supplier_id, amount, operation='add'):
    """
    تحديث رصيد المورد

    المعاملات:
    - session: جلسة قاعدة البيانات
    - supplier_id: معرف المورد
    - amount: المبلغ المراد إضافته أو خصمه
    - operation: نوع العملية ('add' للإضافة، 'subtract' للخصم)

    ملاحظة: القيمة الموجبة تعني أن المبلغ للمورد، والقيمة السالبة تعني أن المبلغ على المورد
    """
    supplier = session.query(Supplier).filter_by(id=supplier_id).first()
    if supplier:
        if operation == 'add':
            supplier.balance += amount
        elif operation == 'subtract':
            supplier.balance -= amount
        session.commit()
        return True, supplier.balance
    return False, 0

# دوال تحسين قاعدة البيانات المتقدمة
def optimize_database(session):
    """تحسين قاعدة البيانات للحصول على أداء 100%"""
    try:
        print("🔧 بدء تحسين قاعدة البيانات...")

        # تحليل وتحسين الفهارس
        session.execute(text("ANALYZE"))
        print("✅ تم تحليل الفهارس")

        # تحسين الإحصائيات
        session.execute(text("PRAGMA optimize"))
        print("✅ تم تحسين الإحصائيات")

        # تنظيف المساحة المهدرة
        session.execute(text("PRAGMA incremental_vacuum"))
        print("✅ تم تنظيف المساحة المهدرة")

        # إعادة بناء الفهارس
        session.execute(text("REINDEX"))
        print("✅ تم إعادة بناء الفهارس")

        # تحديث إحصائيات الجداول
        tables = ['clients', 'suppliers', 'employees', 'invoices', 'inventory',
                 'expenses', 'revenues', 'sales', 'purchases', 'projects']

        for table in tables:
            try:
                session.execute(text(f"ANALYZE {table}"))
                print(f"✅ تم تحليل جدول {table}")
            except Exception as e:
                print(f"⚠️ تحذير في تحليل جدول {table}: {e}")

        session.commit()
        print("🎉 تم تحسين قاعدة البيانات بنجاح!")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحسين قاعدة البيانات: {e}")
        session.rollback()
        return False

def get_database_stats(session):
    """الحصول على إحصائيات قاعدة البيانات"""
    try:
        stats = {}

        # حجم قاعدة البيانات
        result = session.execute(text("PRAGMA page_count")).fetchone()
        page_count = result[0] if result else 0

        result = session.execute(text("PRAGMA page_size")).fetchone()
        page_size = result[0] if result else 4096

        stats['database_size_mb'] = (page_count * page_size) / (1024 * 1024)

        # عدد الجداول
        result = session.execute(text(
            "SELECT COUNT(*) FROM sqlite_master WHERE type='table'"
        )).fetchone()
        stats['table_count'] = result[0] if result else 0

        # عدد الفهارس
        result = session.execute(text(
            "SELECT COUNT(*) FROM sqlite_master WHERE type='index'"
        )).fetchone()
        stats['index_count'] = result[0] if result else 0

        # إحصائيات الجداول الرئيسية
        main_tables = ['clients', 'suppliers', 'employees', 'invoices', 'inventory']
        for table in main_tables:
            try:
                result = session.execute(text(f"SELECT COUNT(*) FROM {table}")).fetchone()
                stats[f'{table}_count'] = result[0] if result else 0
            except:
                stats[f'{table}_count'] = 0

        return stats

    except Exception as e:
        print(f"خطأ في الحصول على إحصائيات قاعدة البيانات: {e}")
        return {}

def create_performance_indexes(session):
    """إنشاء فهارس الأداء المتقدمة

    ملاحظة: تم إزالة الفهارس التي تحتوي على دوال تاريخ ديناميكية مثل datetime('now')
    لأن SQLite لا يدعم الدوال غير المحددة (non-deterministic) في شروط الفهارس
    """
    try:
        print("🔧 إنشاء فهارس الأداء المتقدمة...")

        # فهارس إضافية للبحث السريع
        performance_indexes = [
            # فهارس البحث النصي
            "CREATE INDEX IF NOT EXISTS idx_client_name_search ON clients(name COLLATE NOCASE)",
            "CREATE INDEX IF NOT EXISTS idx_supplier_name_search ON suppliers(name COLLATE NOCASE)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_name_search ON inventory(name COLLATE NOCASE)",

            # فهارس التواريخ المتقدمة
            "CREATE INDEX IF NOT EXISTS idx_invoice_date_year ON invoices(strftime('%Y', date))",
            "CREATE INDEX IF NOT EXISTS idx_invoice_date_month ON invoices(strftime('%Y-%m', date))",
            "CREATE INDEX IF NOT EXISTS idx_expense_date_year ON expenses(strftime('%Y', date))",
            "CREATE INDEX IF NOT EXISTS idx_revenue_date_year ON revenues(strftime('%Y', date))",

            # فهارس الأرصدة والمبالغ
            "CREATE INDEX IF NOT EXISTS idx_client_positive_balance ON clients(balance) WHERE balance > 0",
            "CREATE INDEX IF NOT EXISTS idx_client_negative_balance ON clients(balance) WHERE balance < 0",
            "CREATE INDEX IF NOT EXISTS idx_supplier_positive_balance ON suppliers(balance) WHERE balance > 0",

            # فهارس الحالة المتقدمة
            "CREATE INDEX IF NOT EXISTS idx_invoice_pending ON invoices(status, due_date) WHERE status = 'pending'",
            # تم إزالة فهرس الفواتير المتأخرة لتجنب استخدام datetime('now')

            # فهارس المخزون المتقدمة
            "CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(quantity, min_quantity) WHERE quantity <= min_quantity",
            "CREATE INDEX IF NOT EXISTS idx_inventory_profit_margin ON inventory(selling_price - cost_price)",

            # فهارس الرواتب واليوميات
            "CREATE INDEX IF NOT EXISTS idx_salary_employee_date ON salaries(employee_id, payment_date)",
            "CREATE INDEX IF NOT EXISTS idx_daily_wage_employee_date ON daily_wages(employee_id, wage_date)",

            # فهارس الوثائق
            "CREATE INDEX IF NOT EXISTS idx_document_type_date ON documents(file_type, upload_date)",
            "CREATE INDEX IF NOT EXISTS idx_document_client_type ON documents(client_id, file_type)",

            # فهارس عناصر الفواتير والمبيعات
            "CREATE INDEX IF NOT EXISTS idx_invoice_item_invoice_price ON invoice_items(invoice_id, total_price)",
            "CREATE INDEX IF NOT EXISTS idx_sale_item_sale_price ON sale_items(sale_id, total_price)",
            "CREATE INDEX IF NOT EXISTS idx_purchase_item_purchase_price ON purchase_items(purchase_id, total_price)",

            # فهارس خاصة للتقارير ولوحة المعلومات
            "CREATE INDEX IF NOT EXISTS idx_dashboard_client_balance ON clients(balance) WHERE balance != 0",
            "CREATE INDEX IF NOT EXISTS idx_dashboard_supplier_balance ON suppliers(balance) WHERE balance != 0",
            "CREATE INDEX IF NOT EXISTS idx_dashboard_invoice_pending ON invoices(status, due_date) WHERE status = 'pending'",
            "CREATE INDEX IF NOT EXISTS idx_dashboard_inventory_alert ON inventory(quantity, min_quantity) WHERE quantity <= min_quantity",

            # فهارس التقارير المالية
            "CREATE INDEX IF NOT EXISTS idx_report_revenue_monthly ON revenues(strftime('%Y-%m', date), amount)",
            "CREATE INDEX IF NOT EXISTS idx_report_expense_monthly ON expenses(strftime('%Y-%m', date), amount)",
            "CREATE INDEX IF NOT EXISTS idx_report_sale_monthly ON sales(strftime('%Y-%m', date), total_amount)",
            "CREATE INDEX IF NOT EXISTS idx_report_purchase_monthly ON purchases(strftime('%Y-%m', date), total_amount)",

            # فهارس التقارير التحليلية
            "CREATE INDEX IF NOT EXISTS idx_report_client_activity ON invoices(client_id, date, total_amount)",
            "CREATE INDEX IF NOT EXISTS idx_report_supplier_activity ON purchases(supplier_id, date, total_amount)",
            "CREATE INDEX IF NOT EXISTS idx_report_project_profitability ON projects(budget, total_cost, status)",
            "CREATE INDEX IF NOT EXISTS idx_report_inventory_turnover ON inventory(quantity, cost_price, selling_price)",

            # فهارس الإشعارات والتنبيهات
            "CREATE INDEX IF NOT EXISTS idx_notification_unread ON notifications(is_read, date) WHERE is_read = 0",
            "CREATE INDEX IF NOT EXISTS idx_reminder_upcoming ON reminders(reminder_date, is_completed) WHERE is_completed = 0",
            # تم إزالة فهرس الأحداث اليومية لتجنب استخدام date('now')

            # فهارس الأجور والرواتب (بدون شروط تاريخية ديناميكية)
            "CREATE INDEX IF NOT EXISTS idx_salary_year_month ON salaries(year, month, employee_id)",
            "CREATE INDEX IF NOT EXISTS idx_daily_wage_monthly ON daily_wages(strftime('%Y-%m', wage_date), employee_id)",
        ]

        for index_sql in performance_indexes:
            try:
                session.execute(text(index_sql))
                print(f"✅ تم إنشاء فهرس الأداء")
            except Exception as e:
                print(f"⚠️ تحذير في إنشاء فهرس: {e}")

        session.commit()
        print("🎉 تم إنشاء جميع فهارس الأداء بنجاح!")
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء فهارس الأداء: {e}")
        session.rollback()
        return False

def init_database_with_optimization():
    """تهيئة قاعدة البيانات مع التحسينات المتقدمة"""
    try:
        print("🚀 تهيئة قاعدة البيانات مع التحسينات المتقدمة...")

        # إنشاء الجداول
        Base.metadata.create_all(engine)
        print("✅ تم إنشاء الجداول")

        # إنشاء جلسة للتحسين
        session = get_session()

        # إنشاء فهارس الأداء
        create_performance_indexes(session)

        # تحسين قاعدة البيانات
        optimize_database(session)

        # عرض الإحصائيات
        stats = get_database_stats(session)
        print(f"📊 إحصائيات قاعدة البيانات:")
        print(f"   - حجم قاعدة البيانات: {stats.get('database_size_mb', 0):.2f} MB")
        print(f"   - عدد الجداول: {stats.get('table_count', 0)}")
        print(f"   - عدد الفهارس: {stats.get('index_count', 0)}")
        print(f"   - عدد العملاء: {stats.get('clients_count', 0)}")
        print(f"   - عدد الفواتير: {stats.get('invoices_count', 0)}")

        session.close()
        print("🎉 تم تهيئة قاعدة البيانات بنجاح مع تحسينات 100%!")
        return True

    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False

def optimize_dashboard_performance(session):
    """تحسين أداء لوحة المعلومات والتقارير"""
    try:
        print("📊 تحسين أداء لوحة المعلومات...")

        # فهارس خاصة للوحة المعلومات
        dashboard_indexes = [
            # فهارس الإحصائيات السريعة
            "CREATE INDEX IF NOT EXISTS idx_dashboard_stats_clients ON clients(id) WHERE balance != 0",
            "CREATE INDEX IF NOT EXISTS idx_dashboard_stats_suppliers ON suppliers(id) WHERE balance != 0",
            "CREATE INDEX IF NOT EXISTS idx_dashboard_stats_invoices ON invoices(id) WHERE status = 'pending'",
            "CREATE INDEX IF NOT EXISTS idx_dashboard_stats_inventory ON inventory(id) WHERE quantity <= min_quantity",

            # فهارس الرسوم البيانية (بدون شروط تاريخية ديناميكية)
            "CREATE INDEX IF NOT EXISTS idx_chart_revenue_trend ON revenues(date, amount)",
            "CREATE INDEX IF NOT EXISTS idx_chart_expense_trend ON expenses(date, amount)",
            "CREATE INDEX IF NOT EXISTS idx_chart_sales_trend ON sales(date, total_amount)",

            # فهارس التنبيهات الفورية (بدون شروط تاريخية ديناميكية)
            "CREATE INDEX IF NOT EXISTS idx_alert_overdue_invoices ON invoices(due_date, status)",
            "CREATE INDEX IF NOT EXISTS idx_alert_low_inventory ON inventory(name, quantity, min_quantity) WHERE quantity <= min_quantity",
            "CREATE INDEX IF NOT EXISTS idx_alert_high_balances ON clients(name, balance) WHERE abs(balance) > 10000",
        ]

        for index_sql in dashboard_indexes:
            try:
                session.execute(text(index_sql))
                print(f"✅ تم إنشاء فهرس لوحة المعلومات")
            except Exception as e:
                print(f"⚠️ تحذير في إنشاء فهرس لوحة المعلومات: {e}")

        session.commit()
        print("🎉 تم تحسين أداء لوحة المعلومات بنجاح!")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحسين لوحة المعلومات: {e}")
        session.rollback()
        return False

def optimize_reports_performance(session):
    """تحسين أداء التقارير"""
    try:
        print("📈 تحسين أداء التقارير...")

        # فهارس خاصة للتقارير
        reports_indexes = [
            # تقارير العملاء
            "CREATE INDEX IF NOT EXISTS idx_report_client_summary ON invoices(client_id, date, total_amount, paid_amount)",
            "CREATE INDEX IF NOT EXISTS idx_report_client_aging ON invoices(client_id, due_date, status) WHERE status = 'pending'",

            # تقارير الموردين
            "CREATE INDEX IF NOT EXISTS idx_report_supplier_summary ON purchases(supplier_id, date, total_amount, paid_amount)",
            "CREATE INDEX IF NOT EXISTS idx_report_supplier_performance ON purchases(supplier_id, status, date)",

            # تقارير المشاريع
            "CREATE INDEX IF NOT EXISTS idx_report_project_summary ON projects(status, budget, total_cost, start_date)",
            "CREATE INDEX IF NOT EXISTS idx_report_project_profitability ON projects(client_id, budget, total_cost)",

            # تقارير الأرباح والخسائر
            "CREATE INDEX IF NOT EXISTS idx_report_profit_loss_revenue ON revenues(date, amount, category)",
            "CREATE INDEX IF NOT EXISTS idx_report_profit_loss_expense ON expenses(date, amount, category)",

            # تقارير المخزون
            "CREATE INDEX IF NOT EXISTS idx_report_inventory_valuation ON inventory(cost_price, quantity, selling_price)",
            "CREATE INDEX IF NOT EXISTS idx_report_inventory_movement ON sale_items(inventory_id, quantity, unit_price)",

            # تقارير الموظفين
            "CREATE INDEX IF NOT EXISTS idx_report_employee_salary ON salaries(employee_id, year, month, amount)",
            "CREATE INDEX IF NOT EXISTS idx_report_employee_wages ON daily_wages(employee_id, wage_date, total_amount)",
        ]

        for index_sql in reports_indexes:
            try:
                session.execute(text(index_sql))
                print(f"✅ تم إنشاء فهرس التقارير")
            except Exception as e:
                print(f"⚠️ تحذير في إنشاء فهرس التقارير: {e}")

        session.commit()
        print("🎉 تم تحسين أداء التقارير بنجاح!")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحسين التقارير: {e}")
        session.rollback()
        return False
